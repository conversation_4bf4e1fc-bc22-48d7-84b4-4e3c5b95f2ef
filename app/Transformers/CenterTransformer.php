<?php

namespace App\Transformers;

use Illuminate\Support\Collection;

class CenterTransformer
{
    /**
     * 將現役人員資料轉換為前端所需格式
     */
    public function transformRoleGroups($activeStaff)
    {
        // 按角色分組
        $roleGroups = [];
        foreach ($activeStaff as $staff) {
            $roleCode = $staff->role_code;
            if (!isset($roleGroups[$roleCode])) {
                $roleGroups[$roleCode] = [];
            }
            $roleGroups[$roleCode][] = [
                'account_id' => $staff->account_id,
                'account_number' => $staff->account_number,
                'account_name' => $staff->account_name,
                'start_at' => $staff->start_at
            ];
        }

        // 轉換為前端所需格式
        return [
            'total_count' => count($activeStaff),
            'roles' => $roleGroups
        ];
    }

    /**
     * 將中心資料轉換為前端所需格式
     */
    public function transformCenterData($center)
    {
        return [
            'id' => $center->id,
            'name' => $center->name,
            'center_level_id' => $center->center_level_id,
            'status' => $center->status
        ];
    }

    /**
     * 將會員資料轉換為自動完成所需格式
     */
    public function transformAccountSuggestions(Collection $accounts): Collection
    {
        return $accounts->map(function ($account) {
            return [
                'id' => $account->id,
                'number' => $account->number,
                'name' => $account->name ?? '',
                'label' => $account->number . ($account->name ? ' - ' . $account->name : ''),
                'value' => $account->number,
                'status' => $account->status ?? 1
            ];
        });
    }
}
