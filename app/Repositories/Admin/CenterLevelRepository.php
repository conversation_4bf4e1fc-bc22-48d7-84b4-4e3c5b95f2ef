<?php

namespace App\Repositories\Admin;

use App\Models\Main\CenterLevel;
use Illuminate\Support\Collection;

class CenterLevelRepository
{
    public function __construct(protected CenterLevel $centerLevel) {}

    /**
     * 取得中心等級選項
     */
    public function getCenterLevels()
    {
        return $this->centerLevel->orderBy('id')->get();
    }

    /**
     * 取得所有可用的中心等級
     *
     * @return Collection
     */
    public function getAllCenterLevels(): Collection
    {
        return $this->centerLevel
            ->where('status', 1)
            ->orderBy('id')
            ->get();
    }
}
