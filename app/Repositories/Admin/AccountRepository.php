<?php

namespace App\Repositories\Admin;

use App\Models\Main\Account;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AccountRepository
{
    public function __construct(protected Account $account) {}

    /**
     * 搜尋會員，支援自動完成
     *
     * @param string $query 搜尋關鍵字
     * @param int $limit 限制結果數量
     * @return Collection
     */
    public function searchByNumber(string $query, int $limit = 10): Collection
    {
        $cleanQuery = trim($query);
        $safeLimit = min($limit, 100);

        if (empty($cleanQuery)) {
            return collect();
        }

        return $this->account
            ->where('number', 'like', '%' . $cleanQuery . '%')
            ->where('status', 1)
            ->select(['id', 'number', 'name', 'email', 'status'])
            ->orderBy('number')
            ->limit($safeLimit)
            ->get();
    }
    /**
     * 搜尋會員編號或姓名，支援自動完成
     *
     * @param string $query 搜尋關鍵字
     * @param int $limit 限制結果數量
     * @return Collection
     */
    public function searchByNumberOrName(string $query, int $limit = 10): Collection
    {
        $cleanQuery = trim($query);
        $safeLimit = min($limit, 100);

        if (empty($cleanQuery)) {
            return collect();
        }

        return $this->account
            ->where(function ($q) use ($cleanQuery) {
                $q->where('number', 'like', '%' . $cleanQuery . '%')
                    ->orWhere('name', 'like', '%' . $cleanQuery . '%');
            })
            ->where('status', 1)
            ->select(['id', 'number', 'name', 'email', 'status'])
            ->orderBy('number')
            ->limit($safeLimit)
            ->get();
    }

    /**
     * 根據會員編號查找會員
     */
    public function findByNumber(string $number)
    {
        return $this->account->where('number', $number)->first();
    }

    /**
     * 根據ID查找會員
     */
    public function findById(int $id)
    {
        return $this->account->where('id', $id)->first();
    }
}
