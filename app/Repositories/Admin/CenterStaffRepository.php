<?php

namespace App\Repositories\Admin;

use App\Models\Main\CenterStaff;

class CenterStaffRepository
{
    public function __construct(protected CenterStaff $centerStaff) {}

    /**
     * 取得指定中心現役人員資料
     *
     * @param int $centerId
     * @return \Illuminate\Support\Collection
     */
    public function getActiveStaffByCenterId($centerId)
    {
        return CenterStaff::with(['role', 'account'])
            ->active()
            ->forCenter($centerId)
            ->orderBy('role_id')
            ->orderBy('start_at')
            ->get()
            ->map(function ($staff) {
                return [
                    'role_id'        => $staff->role_id,
                    'role_code'      => $staff->role->code ?? null,
                    'role_name'      => $staff->role->name ?? null,
                    'account_id'     => $staff->account_id,
                    'account_number' => $staff->account->number ?? null,
                    'account_name'   => $staff->account->name ?? null,
                    'start_at'       => $staff->start_at,
                ];
            });
    }

    /**
     * 取得指定中心現役人員數量
     *
     * @param int $centerId
     * @return int
     */
    public function getActiveStaffCountByCenterId(int $centerId): int
    {
        return $this->centerStaff->where('center_id', $centerId)
            ->active()
            ->count();
    }

    /**
     * 取得某中心某角色現役集合
     */
    public function getActiveStaffByRole(int $centerId, int $roleId)
    {
        return $this->centerStaff->where('center_id', $centerId)
            ->where('role_id', $roleId)
            ->active()
            ->get();
    }

    /**
     * 檢查單例角色是否已存在現役
     */
    public function hasActiveSingleton(int $centerId, int $roleId): bool
    {
        return $this->centerStaff->where('center_id', $centerId)
            ->where('role_id', $roleId)
            ->active()
            ->exists();
    }

    /**
     * 根據中心、會員、角色查找現役記錄
     */
    public function findActiveStaff(int $centerId, int $accountId, int $roleId): ?CenterStaff
    {
        return $this->centerStaff->where('center_id', $centerId)
            ->where('account_id', $accountId)
            ->where('role_id', $roleId)
            ->active()
            ->first();
    }

    /**
     * 創建員工記錄
     */
    public function create(array $data): CenterStaff
    {
        return $this->centerStaff->create($data);
    }

    /**
     * 更新員工記錄
     */
    public function update(CenterStaff $staff, array $data): bool
    {
        return $staff->update($data);
    }

    /**
     * 根據ID查找員工記錄
     */
    public function findOrFail(int $id): CenterStaff
    {
        return $this->centerStaff->findOrFail($id);
    }
}
